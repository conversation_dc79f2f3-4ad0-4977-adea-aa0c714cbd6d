

<!-- Loading Overlay -->
<div *ngIf="isFormLoading" class="loading-overlay">
  <div class="loading-spinner">
    <div class="spinner"></div>
    <p> Loading...</p>
  </div>
</div>

<!-- Form Actions Component -->
<app-form-actions #formActions
  [form]="form"
  [tableName]="tableName"
  [screenName]="screenName"
  [isTenantBasedFlag]="isTenantBasedFlag"
  [authorizeNumber]="authorizeNumber"
  [isViewMode]="isViewMode"
  [showSuccessPopup]="showSuccessPopup"
  [successMessage]="successMessage"
  [errorMessage]="errorMessage"
  [isLoading]="isLoading"
  [validationResult]="validationResult"
  [fields]="fields"
  [hasSubScreens]="hasSubScreens"
  [subScreenForms]="subScreenForms"
  [subScreens]="subScreens"
  [subScreensMetadata]="subScreensMetadata"
  (submissionSuccess)="onSubmissionSuccess($event)"
  (errorMessageChange)="onErrorMessageChange($event)"
  (isLoadingChange)="onIsLoadingChange($event)"
  (showSuccessPopupChange)="onShowSuccessPopupChange($event)"
  (successMessageChange)="onSuccessMessageChange($event)"
  (validationResultChange)="onValidationResultChange($event)"
  (goBackRequested)="onGoBackRequested()"
  (setFormReadonly)="onSetFormReadonly($event)"
  (populateForm)="onPopulateForm($event)"
  (populateDefaultFields)="onPopulateDefaultFields($event)"
  (setViewMode)="onSetViewMode($event)">
</app-form-actions>

@if (!submissionSuccess) {
  @if (showInitialInput) {
    <app-initial-input
      [form]="form"
      [tableName]="tableName"
      [screenName]="screenName"
      [showValidation]="showValidation"
      (loadDataAndBuildForm)="loadDataAndBuildForm()"
      (viewData)="viewData()"
      (validationChange)="onValidationChange($event)">
    </app-initial-input>
  }

  @if (errorMessage) {
    <div class="error-message">{{ errorMessage }}</div>
  }

  @if (!showInitialInput) {
    <div class="form-grid">
    <form [formGroup]="form">
      <app-form-header
        [form]="form"
        [isViewMode]="isViewMode"
        [isAuth]="isAuth"
        [isRowView]="isRowView"
        [errorMessage]="errorMessage"
        (toggleViewMode)="toggleViewMode()"
        (submitForm)="onFormSubmit()"
        (validateRecord)="onFormValidate()"
        (authorizeRecord)="onFormAuthorize()"
        (goBack)="goBack()"
        (rejectRecord)="onFormReject()"
        (deleteRecord)="onFormDelete()"
        (setFormLoading)="onSetFormLoading($event)">
      </app-form-header>
      <!-- 🔹 All Fields in Order (Unified Rendering) -->

      <!-- Parallel System (Matrix-based positioning) -->
      @if (useParallelSystem && positioningResult?.matrix) {
        <div class="form-matrix"
             [style.grid-template-rows]="'repeat(' + (positioningResult!.maxRows || 1) + ', auto)'"
             [style.grid-template-columns]="'repeat(' + (positioningResult!.maxColumns || 1) + ', 1fr)'">
          @for (field of (positioningResult!.fields || []); track field.fieldName) {
            @if (field.fieldName.toUpperCase() !== 'ID') {
              <div class="form-field-positioned" [ngStyle]="getFieldGridStyles(field)">
                <!-- 🔸 Non-Grouped Fields -->
                @if (!field.Group) {
                  <!-- 🔸 Regular Field Component (non-multi) -->
                  @if (!field.isMulti) {
                    <app-regular-field
                      [field]="field"
                      [form]="form"
                      [isViewMode]="isViewMode"
                      [fields]="fields"
                      (fieldValueChange)="onFieldValueChange($event)">
                    </app-regular-field>
                  }
                  <!-- Multi-field and grouped field content will be added here -->
                }
              </div>
            }
          }
        </div>
      }

      <!-- Round-Robin System (Column-based positioning) -->
      @if (!useParallelSystem) {
        <div class="form-grid" [ngClass]="'columns-' + columnCount">
          <div class="form-column" *ngFor="let column of columns">
            <ng-container *ngFor="let field of column">
            <!-- Skip ID field as it's handled separately -->
            @if (field.fieldName?.toUpperCase() !== 'ID') {

                <!-- 🔸 Non-Grouped Fields -->
                @if (!field.Group) {

                  <!-- 🔸 Regular Field Component (non-multi) -->
                  @if (!field.isMulti) {
                    <app-regular-field
                      [field]="field"
                      [form]="form"
                      [isViewMode]="isViewMode"
                      [fields]="fields"
                      (fieldValueChange)="onFieldValueChange($event)">
                    </app-regular-field>
                  }

                  <!-- ======================================== -->
                  <!-- MULTI FIELD COMPONENT SECTION -->
                  <!-- ======================================== -->
                  <!-- 🔸 Multi-Field (Non-Grouped) -->
                  @if (field.isMulti) {
                    <div [formArrayName]="field.fieldName">
                      @for (control of getMultiArray(field.fieldName).controls; track control; let j = $index) {
                        <div [formGroupName]="j" class="form-field is-multi">
                          <div class="multi-input-container">
                            <div class="multi-input">
                              <app-regular-field
                                [field]="field"
                                [form]="$any(control)"
                                [isViewMode]="isViewMode"
                                [fields]="fields"
                                [multiIndex]="j + 1"
                                (fieldValueChange)="onFieldValueChange($event)">
                              </app-regular-field>
                            </div>

                            <div class="multi-buttons">
                              @if (getMultiArray(field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                                <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j)" matTooltip="Delete">
                                  <mat-icon>delete</mat-icon>
                                </button>
                              }

                              @if (!isViewMode && !field.noInput) {
                                <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j)" matTooltip="Add">
                                  <mat-icon>add</mat-icon>
                                </button>
                              }
                            </div>
                          </div>
                        </div>
                      }
                    </div>
                  }
                }

                <!-- ======================================== -->
                <!-- GROUPED FIELDS COMPONENT SECTION -->
                <!-- ======================================== -->
                <!-- 🔸 Grouped Fields -->
                @if (field.Group && isFirstFieldInParentGroup(field)) {
                  @let parsed = parseGroupPath(field.Group);
                  @if (parsed.parent) {
                    <div [formArrayName]="parsed.parent" class="grouped-field-section">
                      <h3>{{ parsed.parent }}</h3>
                      @for (group of getGroupArray(parsed.parent).controls; track group; let k = $index) {
                        <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">

                          @if (isRowView) {
                            <!-- Row View: All fields in a single table-like row -->
                            <div class="row-view-table-container">
                              <!-- Parent group fields -->
                              @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {
                                @if (!groupField.isMulti) {
                                  <div class="row-view-table-cell">
                                    <app-regular-field
                                      [field]="groupField"
                                      [form]="$any(group)"
                                      [isViewMode]="isViewMode"
                                      [fields]="fields"
                                      [groupIndex]="k"
                                      (fieldValueChange)="onFieldValueChange($event)">
                                    </app-regular-field>
                                  </div>
                                }

                                <!-- Multi-fields for parent group -->
                                @if (groupField.isMulti) {
                                  <div class="row-view-table-cell-multi" [formArrayName]="groupField.fieldName">
                                    <label>{{ groupField.fieldName }}</label>
                                    @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                                      <div [formGroupName]="l" class="row-view-multi-item">
                                        <div class="row-view-multi-input">
                                          <app-regular-field
                                            [field]="groupField"
                                            [form]="$any(multiControl)"
                                            [isViewMode]="isViewMode"
                                            [fields]="fields"
                                            [multiIndex]="l + 1"
                                            [groupIndex]="k"
                                            (fieldValueChange)="onFieldValueChange($event)">
                                          </app-regular-field>
                                        </div>
                                        <div class="row-view-multi-buttons">
                                          @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                            <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                              <mat-icon>delete</mat-icon>
                                            </button>
                                          }
                                          @if (!isViewMode && !groupField.noInput) {
                                            <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                              <mat-icon>add</mat-icon>
                                            </button>
                                          }
                                        </div>
                                      </div>
                                    }
                                  </div>
                                }
                              }

                              <!-- Nested group fields -->
                              @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                                @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                                  @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {
                                    @if (!nestedField.isMulti) {
                                      <div class="row-view-table-cell" [formArrayName]="childGroup" [formGroupName]="n">
                                        <app-regular-field
                                          [field]="nestedField"
                                          [form]="$any(nestedGroup)"
                                          [isViewMode]="isViewMode"
                                          [fields]="fields"
                                          [groupIndex]="k"
                                          [nestedGroupIndex]="n"
                                          (fieldValueChange)="onFieldValueChange($event)">
                                        </app-regular-field>
                                      </div>
                                    }

                                    <!-- Multi-fields for nested groups -->
                                    @if (nestedField.isMulti) {
                                      <div class="row-view-table-cell-multi" [formArrayName]="childGroup" [formGroupName]="n">
                                        <div [formArrayName]="nestedField.fieldName">
                                          <label>{{ nestedField.fieldName }}</label>
                                          @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                            <div [formGroupName]="m" class="row-view-multi-item">
                                              <div class="row-view-multi-input">
                                                <app-regular-field
                                                  [field]="nestedField"
                                                  [form]="$any(multiControl)"
                                                  [isViewMode]="isViewMode"
                                                  [fields]="fields"
                                                  [multiIndex]="m + 1"
                                                  [groupIndex]="k"
                                                  [nestedGroupIndex]="n"
                                                  (fieldValueChange)="onFieldValueChange($event)">
                                                </app-regular-field>
                                              </div>
                                              <div class="row-view-multi-buttons">
                                                @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                                  <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                    <mat-icon>delete</mat-icon>
                                                  </button>
                                                }
                                                @if (!isViewMode && !nestedField.noInput) {
                                                  <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                    <mat-icon>add</mat-icon>
                                                  </button>
                                                }
                                              </div>
                                            </div>
                                          }
                                        </div>
                                      </div>
                                    }
                                  }

                                  <!-- Nested group control buttons -->
                                  <div class="row-view-table-actions" [formArrayName]="childGroup" [formGroupName]="n">
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                        <mat-icon>delete</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                        <mat-icon>add</mat-icon>
                                      </button>
                                    }
                                    @if (!isViewMode) {
                                      <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                        <mat-icon>content_copy</mat-icon>
                                      </button>
                                    }
                                  </div>
                                }
                              }

                              <!-- Main group action buttons -->
                              <div class="row-view-table-actions">
                                @if (!isViewMode) {
                                  <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                  <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                                    <mat-icon>add</mat-icon>
                                  </button>
                                  <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                                    <mat-icon>content_copy</mat-icon>
                                  </button>
                                }
                              </div>
                            </div>
                          } @else {
                            <!-- Nested View: Original layout -->
                            <div [class]="isRowView ? 'group-fields row-view-fields' : 'group-fields'">

                              <!-- Direct fields of parent group -->
                              @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {

                                @if (!groupField.isMulti) {
                                  <app-regular-field
                                    [field]="groupField"
                                    [form]="$any(group)"
                                    [isViewMode]="isViewMode"
                                    [fields]="fields"
                                    [groupIndex]="k"
                                    (fieldValueChange)="onFieldValueChange($event)">
                                  </app-regular-field>
                                }

                                @if (groupField.isMulti) {
                                  <div [formArrayName]="groupField.fieldName">
                                    <h4>{{ groupField.fieldName }}</h4>

                                    @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                                      <div [formGroupName]="l" class="form-field">
                                        <div class="multi-input-container">
                                          <div class="multi-input">
                                            <app-regular-field
                                              [field]="groupField"
                                              [form]="$any(multiControl)"
                                              [isViewMode]="isViewMode"
                                              [fields]="fields"
                                              [multiIndex]="l + 1"
                                              [groupIndex]="k"
                                              (fieldValueChange)="onFieldValueChange($event)">
                                            </app-regular-field>
                                          </div>

                                          <div class="multi-buttons">
                                            @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                              <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                                <mat-icon>delete</mat-icon>
                                              </button>
                                            }

                                            @if (!isViewMode && !groupField.noInput) {
                                              <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                                <mat-icon>add</mat-icon>
                                              </button>
                                            }
                                          </div>
                                        </div>
                                      </div>
                                    }
                                  </div>
                                }
                              }

                              <!-- Nested subgroups -->
                              @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                                <div [formArrayName]="childGroup" [class]="isRowView ? 'nested-group-section row-view-nested-seamless' : 'nested-group-section'">
                                  <h4 [class.row-view-hidden]="isRowView">{{ childGroup }}</h4>
                                  @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                                    <div [formGroupName]="n" [class]="isRowView ? 'form-grid nested-field row-view-nested-field-seamless' : 'form-grid nested-field'">
                                      <div [class]="isRowView ? 'nested-group-fields row-view-nested-fields-seamless' : 'nested-group-fields'">
                                        @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {

                                          @if (!nestedField.isMulti) {
                                            <app-regular-field
                                              [field]="nestedField"
                                              [form]="$any(nestedGroup)"
                                              [isViewMode]="isViewMode"
                                              [fields]="fields"
                                              [groupIndex]="k"
                                              [nestedGroupIndex]="n"
                                              (fieldValueChange)="onFieldValueChange($event)">
                                            </app-regular-field>
                                          }

                                          @if (nestedField.isMulti) {
                                            <div [formArrayName]="nestedField.fieldName">
                                              <h5>{{ nestedField.fieldName }}</h5>

                                              @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                                <div [formGroupName]="m" class="form-field is-multi">
                                                  <div class="multi-input-container">
                                                    <div class="multi-input">
                                                      <app-regular-field
                                                        [field]="nestedField"
                                                        [form]="$any(multiControl)"
                                                        [isViewMode]="isViewMode"
                                                        [fields]="fields"
                                                        [multiIndex]="m + 1"
                                                        [groupIndex]="k"
                                                        [nestedGroupIndex]="n"
                                                        (fieldValueChange)="onFieldValueChange($event)">
                                                      </app-regular-field>
                                                    </div>

                                                    <div class="multi-buttons">
                                                      @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                                        <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                          <mat-icon>delete</mat-icon>
                                                        </button>
                                                      }

                                                      @if (!isViewMode && !nestedField.noInput) {
                                                        <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                          <mat-icon>add</mat-icon>
                                                        </button>
                                                      }
                                                    </div>
                                                  </div>
                                                </div>
                                              }
                                            </div>
                                          }
                                        }

                                        <!-- Nested group control buttons -->
                                        <div class="nested-group-buttons">
                                          @if (!isViewMode) {
                                            <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                              <mat-icon>delete</mat-icon>
                                            </button>
                                          }
                                          @if (!isViewMode) {
                                            <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                              <mat-icon>add</mat-icon>
                                            </button>
                                          }
                                          @if (!isViewMode) {
                                            <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                              <mat-icon>content_copy</mat-icon>
                                            </button>
                                          }
                                        </div>
                                      </div>
                                    </div>
                                  }
                                </div>
                              }

                              <!-- Group control buttons for nested view -->
                              <div class="group-buttons">
                                @if (!isViewMode) {
                                  <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                }
                                @if (!isViewMode) {
                                  <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                                    <mat-icon>add</mat-icon>
                                  </button>
                                }
                                @if (!isViewMode) {
                                  <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                                    <mat-icon>content_copy</mat-icon>
                                  </button>
                                }
                              </div>
                            </div>
                          }
                        </div>
                      }
                    </div>
                  }
                }
              }
            </ng-container>
          </div>
        </div>
      }
      </form>

      <!-- ======================================== -->
      <!-- SUBSCREEN TABS SECTION -->
      <!-- ======================================== -->
      <!--
        Renders SubScreen tabs when SubScreens are present in metadata
        Each tab contains its own form with fields distributed across columns
      -->
      @if (hasSubScreens && subScreens.length > 0) {
        <div class="subscreen-tabs-container">

          <!-- ======================================== -->
          <!-- TAB HEADERS -->
          <!-- ======================================== -->
          <div class="tab-headers">
            @for (subScreen of subScreens; track subScreen; let i = $index) {
              <button
                class="tab-header"
                [class.active]="selectedTabIndex === i"
                (click)="selectTab(i)"
                type="button">
                {{ subScreen }}
              </button>
            }
          </div>

          <!-- ======================================== -->
          <!-- TAB CONTENT -->
          <!-- ======================================== -->
          <div class="tab-content">
            @for (subScreen of subScreens; track subScreen; let i = $index) {
              @if (subScreenForms[subScreen]) {
                <div
                  class="tab-panel"
                  [class.active]="selectedTabIndex === i"
                  [formGroup]="subScreenForms[subScreen]">

                  <!-- SubScreen form fields -->

                  <!-- Parallel System for SubScreen -->
                  @if (isSubScreenParallel(subScreen)) {
                    <div class="form-matrix subscreen-matrix">
                      @for (field of getSubScreenColumns(subScreen)[0]; track field.fieldName) {
                        @if (field.fieldName?.toUpperCase() !== 'ID') {
                          <div class="form-field-positioned" [ngStyle]="getFieldGridStyles(field)">
                            @if (!field.Group && !field.isMulti) {
                              <app-regular-field
                                [field]="field"
                                [form]="getSubScreenForm(subScreen)"
                                [isViewMode]="isViewMode"
                                [fields]="fields"
                                (fieldValueChange)="onFieldValueChange($event)">
                              </app-regular-field>
                            }
                            <!-- Multi-field and grouped field content for subscreens will be added here -->
                          </div>
                        }
                      }
                    </div>
                  }

                  <!-- Round-Robin System for SubScreen -->
                  @if (!isSubScreenParallel(subScreen)) {
                    <div class="form-grid" [ngClass]="'columns-' + getSubScreenColumnCount(subScreen)">
                      <div class="form-column" *ngFor="let column of getSubScreenColumns(subScreen)">
                        <ng-container *ngFor="let field of column">
                          @if (field.fieldName?.toUpperCase() !== 'ID') {

                          <!-- ======================================== -->
                          <!-- NON-GROUPED FIELDS SECTION -->
                          <!-- ======================================== -->
                          @if (!field.Group) {

                            <!-- ======================================== -->
                            <!-- REGULAR FIELD COMPONENT (NON-MULTI) -->
                            <!-- ======================================== -->
                            @if (!field.isMulti) {
                              <app-regular-field
                                [field]="field"
                                [form]="getSubScreenForm(subScreen)"
                                [isViewMode]="isViewMode"
                                [fields]="fields"
                                (fieldValueChange)="onFieldValueChange($event)">
                              </app-regular-field>
                            }

                            <!-- ======================================== -->
                            <!-- MULTI FIELD COMPONENT SECTION -->
                            <!-- ======================================== -->
                            @if (field.isMulti) {
                              <div [formArrayName]="field.fieldName">
                                @for (control of getSubScreenMultiArray(subScreen, field.fieldName).controls; track control; let j = $index) {
                                  <div [formGroupName]="j" class="form-field is-multi">
                                    <div class="multi-input-container">
                                      <div class="multi-input">
                                        <app-regular-field
                                          [field]="field"
                                          [form]="$any(control)"
                                          [isViewMode]="isViewMode"
                                          [fields]="fields"
                                          [multiIndex]="j + 1"
                                          (fieldValueChange)="onFieldValueChange($event)">
                                        </app-regular-field>
                                      </div>

                                      <div class="multi-buttons">
                                        @if (getSubScreenMultiArray(subScreen, field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                                          <button mat-icon-button color="warn" type="button" (click)="removeSubScreenMultiField(subScreen, field.fieldName, j)" matTooltip="Delete">
                                            <mat-icon>delete</mat-icon>
                                          </button>
                                        }

                                        @if (!isViewMode && !field.noInput) {
                                          <button mat-icon-button color="primary" type="button" (click)="addSubScreenMultiField(subScreen, field, undefined, j)" matTooltip="Add">
                                            <mat-icon>add</mat-icon>
                                          </button>
                                        }
                                      </div>
                                    </div>
                                  </div>
                                }
                              </div>
                            }
                          }

                          <!-- ======================================== -->
                          <!-- GROUPED FIELDS SECTION (SIMPLIFIED FOR SUBSCREENS) -->
                          <!-- ======================================== -->
                          @if (field.Group && isFirstFieldInParentGroupForSubScreen(subScreen, field)) {
                            @let parsed = parseGroupPath(field.Group);
                            @if (parsed.parent) {
                              <div [formArrayName]="parsed.parent" class="grouped-field-section">
                                <h3>{{ parsed.parent }}</h3>
                                @for (group of getSubScreenGroupArray(subScreen, parsed.parent).controls; track group; let k = $index) {
                                  <div [formGroupName]="k" class="form-grid multi-field">
                                    <div class="group-fields">
                                      <!-- Direct fields of parent group -->
                                      @for (groupField of getSubScreenFieldsForGroup(subScreen, parsed.parent); track groupField.fieldName) {
                                        @if (!groupField.isMulti) {
                                          <app-regular-field
                                            [field]="groupField"
                                            [form]="$any(group)"
                                            [isViewMode]="isViewMode"
                                            [fields]="fields"
                                            [groupIndex]="k"
                                            (fieldValueChange)="onFieldValueChange($event)">
                                          </app-regular-field>
                                        }

                                        <!-- Multi-fields for parent group -->
                                        @if (groupField.isMulti) {
                                          <div [formArrayName]="groupField.fieldName">
                                            <h4>{{ groupField.fieldName }}</h4>
                                            @for (multiControl of getSubScreenMultiArray(subScreen, groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                                              <div [formGroupName]="l" class="form-field">
                                                <div class="multi-input-container">
                                                  <div class="multi-input">
                                                    <app-regular-field
                                                      [field]="groupField"
                                                      [form]="$any(multiControl)"
                                                      [isViewMode]="isViewMode"
                                                      [fields]="fields"
                                                      [multiIndex]="l + 1"
                                                      [groupIndex]="k"
                                                      (fieldValueChange)="onFieldValueChange($event)">
                                                    </app-regular-field>
                                                  </div>

                                                  <div class="multi-buttons">
                                                    @if (getSubScreenMultiArray(subScreen, groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                                      <button mat-icon-button color="warn" type="button" (click)="removeSubScreenMultiField(subScreen, groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                                        <mat-icon>delete</mat-icon>
                                                      </button>
                                                    }

                                                    @if (!isViewMode && !groupField.noInput) {
                                                      <button mat-icon-button color="primary" type="button" (click)="addSubScreenMultiField(subScreen, groupField, k, l, field.Group)" matTooltip="Add">
                                                        <mat-icon>add</mat-icon>
                                                      </button>
                                                    }
                                                  </div>
                                                </div>
                                              </div>
                                            }
                                          </div>
                                        }
                                      }

                                      <!-- Nested subgroups -->
                                      @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                                        <div [formArrayName]="childGroup" class="nested-group-section">
                                          <h4>{{ childGroup }}</h4>
                                          @for (nestedGroup of getSubScreenNestedGroupArray(subScreen, parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                                            <div [formGroupName]="n" class="form-grid nested-field">
                                              <div class="nested-group-fields">
                                                @for (nestedField of getSubScreenFieldsForGroupPath(subScreen, parsed.parent + '|' + childGroup); track nestedField.fieldName) {
                                                  @if (!nestedField.isMulti) {
                                                    <app-regular-field
                                                      [field]="nestedField"
                                                      [form]="$any(nestedGroup)"
                                                      [isViewMode]="isViewMode"
                                                      [fields]="fields"
                                                      [groupIndex]="k"
                                                      [nestedGroupIndex]="n"
                                                      (fieldValueChange)="onFieldValueChange($event)">
                                                    </app-regular-field>
                                                  }

                                                  @if (nestedField.isMulti) {
                                                    <div [formArrayName]="nestedField.fieldName">
                                                      <h5>{{ nestedField.fieldName }}</h5>
                                                      @for (multiControl of getSubScreenMultiArray(subScreen, nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                                        <div [formGroupName]="m" class="form-field is-multi">
                                                          <div class="multi-input-container">
                                                            <div class="multi-input">
                                                              <app-regular-field
                                                                [field]="nestedField"
                                                                [form]="$any(multiControl)"
                                                                [isViewMode]="isViewMode"
                                                                [fields]="fields"
                                                                [multiIndex]="m + 1"
                                                                [groupIndex]="k"
                                                                [nestedGroupIndex]="n"
                                                                (fieldValueChange)="onFieldValueChange($event)">
                                                              </app-regular-field>
                                                            </div>

                                                            <div class="multi-buttons">
                                                              @if (getSubScreenMultiArray(subScreen, nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                                                <button mat-icon-button color="warn" type="button" (click)="removeSubScreenMultiField(subScreen, nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                                  <mat-icon>delete</mat-icon>
                                                                </button>
                                                              }

                                                              @if (!isViewMode && !nestedField.noInput) {
                                                                <button mat-icon-button color="primary" type="button" (click)="addSubScreenMultiField(subScreen, nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                                  <mat-icon>add</mat-icon>
                                                                </button>
                                                              }
                                                            </div>
                                                          </div>
                                                        </div>
                                                      }
                                                    </div>
                                                  }
                                                }

                                                <!-- Nested group control buttons -->
                                                <div class="nested-group-buttons">
                                                  @if (!isViewMode) {
                                                    <button mat-icon-button color="warn" type="button" (click)="removeSubScreenNestedGroup(subScreen, parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getSubScreenNestedGroupArray(subScreen, parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                                      <mat-icon>delete</mat-icon>
                                                    </button>
                                                  }
                                                  @if (!isViewMode) {
                                                    <button mat-icon-button color="primary" type="button" (click)="addSubScreenNestedGroup(subScreen, parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                                      <mat-icon>add</mat-icon>
                                                    </button>
                                                  }
                                                  @if (!isViewMode) {
                                                    <button mat-icon-button color="accent" type="button" (click)="cloneSubScreenNestedGroup(subScreen, parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                                      <mat-icon>content_copy</mat-icon>
                                                    </button>
                                                  }
                                                </div>
                                              </div>
                                            </div>
                                          }
                                        </div>
                                      }

                                      <!-- Group control buttons -->
                                      <div class="group-buttons">
                                        @if (!isViewMode) {
                                          <button mat-icon-button color="warn" type="button" (click)="removeSubScreenGroup(subScreen, parsed.parent, k)" matTooltip="Remove Group" [disabled]="getSubScreenGroupArray(subScreen, parsed.parent).controls.length <= 1">
                                            <mat-icon>delete</mat-icon>
                                          </button>
                                        }
                                        @if (!isViewMode) {
                                          <button mat-icon-button color="primary" type="button" (click)="addSubScreenGroup(subScreen, parsed.parent, k)" matTooltip="Add Group">
                                            <mat-icon>add</mat-icon>
                                          </button>
                                        }
                                        @if (!isViewMode) {
                                          <button mat-icon-button color="accent" type="button" (click)="cloneSubScreenGroup(subScreen, parsed.parent, k)" matTooltip="Clone Group">
                                            <mat-icon>content_copy</mat-icon>
                                          </button>
                                        }
                                      </div>
                                    </div>
                                  </div>
                                }
                              </div>
                            }
                          }
                        }
                      </ng-container>
                    </div>
                  </div>
                  }
                </div>
              }
            }
          </div>
        </div>
      }
    </div>
  }
} @else {
  <div class="success-message">Record submitted successfully!</div>
}
