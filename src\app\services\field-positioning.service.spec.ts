import { TestBed } from '@angular/core/testing';
import { FieldPositioningService, PositionedField } from './field-positioning.service';
import { CustomField } from '../core/models/custom-field';

describe('FieldPositioningService', () => {
  let service: FieldPositioningService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(FieldPositioningService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Round-Robin Positioning', () => {
    it('should use round-robin when no positioning data is present', () => {
      const fields: CustomField[] = [
        { fieldName: 'field1', type: 'string', mandatory: false, Group: '', isMulti: false, noChange: false },
        { fieldName: 'field2', type: 'string', mandatory: false, Group: '', isMulti: false, noChange: false },
        { fieldName: 'field3', type: 'string', mandatory: false, Group: '', isMulti: false, noChange: false }
      ];

      const result = service.positionFields(fields, 2);

      expect(result.useParallelSystem).toBe(false);
      expect(result.columns).toBeDefined();
      expect(result.columns!.length).toBe(2);
      expect(result.columns![0].length).toBe(2); // field1, field3
      expect(result.columns![1].length).toBe(1); // field2
    });
  });

  describe('Parallel Positioning', () => {
    it('should use parallel system when positioning data is present', () => {
      const fields: CustomField[] = [
        { 
          fieldName: 'field1', 
          type: 'string', 
          mandatory: false, 
          Group: '', 
          isMulti: false, 
          noChange: false,
          row: 1,
          column: 1,
          colSize: 2,
          rowSize: 1
        },
        { 
          fieldName: 'field2', 
          type: 'string', 
          mandatory: false, 
          Group: '', 
          isMulti: false, 
          noChange: false,
          row: 1,
          column: 3,
          colSize: 1,
          rowSize: 1
        }
      ];

      const result = service.positionFields(fields, 2);

      expect(result.useParallelSystem).toBe(true);
      expect(result.matrix).toBeDefined();
      expect(result.maxRows).toBe(1);
      expect(result.maxColumns).toBe(3);
      expect(result.fields.length).toBe(2);
    });

    it('should handle field spanning correctly', () => {
      const fields: CustomField[] = [
        { 
          fieldName: 'field1', 
          type: 'string', 
          mandatory: false, 
          Group: '', 
          isMulti: false, 
          noChange: false,
          row: 1,
          column: 1,
          colSize: 2,
          rowSize: 2
        }
      ];

      const result = service.positionFields(fields, 1);

      expect(result.useParallelSystem).toBe(true);
      expect(result.maxRows).toBe(2);
      expect(result.maxColumns).toBe(2);
      
      const positionedField = result.fields[0] as PositionedField;
      expect(positionedField.gridRowSpan).toBe(2);
      expect(positionedField.gridColumnSpan).toBe(2);
    });

    it('should handle conflicts by using response order priority', () => {
      const fields: CustomField[] = [
        { 
          fieldName: 'field1', 
          type: 'string', 
          mandatory: false, 
          Group: '', 
          isMulti: false, 
          noChange: false,
          row: 1,
          column: 1,
          colSize: 1,
          rowSize: 1
        },
        { 
          fieldName: 'field2', 
          type: 'string', 
          mandatory: false, 
          Group: '', 
          isMulti: false, 
          noChange: false,
          row: 1,
          column: 1,
          colSize: 1,
          rowSize: 1
        }
      ];

      const result = service.positionFields(fields, 1);

      expect(result.useParallelSystem).toBe(true);
      expect(result.fields.length).toBe(2);
      
      // First field should get the requested position
      const firstField = result.fields.find(f => f.fieldName === 'field1') as PositionedField;
      expect(firstField.gridRow).toBe(1);
      expect(firstField.gridColumn).toBe(1);
      
      // Second field should be placed in next available position
      const secondField = result.fields.find(f => f.fieldName === 'field2') as PositionedField;
      expect(secondField.gridRow).toBeGreaterThanOrEqual(1);
      expect(secondField.gridColumn).toBeGreaterThanOrEqual(1);
    });
  });

  describe('Grid Styles', () => {
    it('should generate correct CSS grid styles', () => {
      const field: PositionedField = {
        fieldName: 'test',
        type: 'string',
        mandatory: false,
        Group: '',
        isMulti: false,
        noChange: false,
        gridRow: 2,
        gridColumn: 3,
        gridRowSpan: 2,
        gridColumnSpan: 3
      };

      const styles = service.getFieldGridStyles(field);

      expect(styles['grid-row']).toBe('2 / span 2');
      expect(styles['grid-column']).toBe('3 / span 3');
    });

    it('should handle single cell positioning', () => {
      const field: PositionedField = {
        fieldName: 'test',
        type: 'string',
        mandatory: false,
        Group: '',
        isMulti: false,
        noChange: false,
        gridRow: 1,
        gridColumn: 1,
        gridRowSpan: 1,
        gridColumnSpan: 1
      };

      const styles = service.getFieldGridStyles(field);

      expect(styles['grid-row']).toBe('1');
      expect(styles['grid-column']).toBe('1');
    });

    it('should return empty styles for non-positioned fields', () => {
      const field: PositionedField = {
        fieldName: 'test',
        type: 'string',
        mandatory: false,
        Group: '',
        isMulti: false,
        noChange: false
      };

      const styles = service.getFieldGridStyles(field);

      expect(Object.keys(styles).length).toBe(0);
    });
  });
});
