import { Injectable } from '@angular/core';
import { CustomField } from '../core/models/custom-field';

export interface PositionedField extends CustomField {
  gridRow?: number;
  gridColumn?: number;
  gridRowSpan?: number;
  gridColumnSpan?: number;
  originalIndex?: number;
}

export interface MatrixCell {
  field: PositionedField | null;
  occupied: boolean;
  rowSpan: number;
  colSpan: number;
}

export interface PositioningResult {
  useParallelSystem: boolean;
  fields: PositionedField[];
  matrix?: MatrixCell[][];
  maxRows?: number;
  maxColumns?: number;
  columns?: PositionedField[][]; // For round-robin result
}

@Injectable({
  providedIn: 'root'
})
export class FieldPositioningService {

  constructor() { }

  /**
   * Main method to position fields using either parallel or round-robin system
   * @param fields Array of fields to position
   * @param columnCount Number of columns for round-robin (ignored for parallel)
   * @returns PositioningResult with positioned fields and layout information
   */
  positionFields(fields: CustomField[], columnCount: number = 1): PositioningResult {
    // Check if any field has positioning data
    const hasPositioningData = this.hasPositioningData(fields);
    
    if (hasPositioningData) {
      return this.positionFieldsParallel(fields);
    } else {
      return this.positionFieldsRoundRobin(fields, columnCount);
    }
  }

  /**
   * Check if any field in the array has positioning data
   * @param fields Array of fields to check
   * @returns true if any field has row, column, colSize, or rowSize properties
   */
  private hasPositioningData(fields: CustomField[]): boolean {
    return fields.some(field => 
      field.row !== undefined || 
      field.column !== undefined || 
      field.colSize !== undefined || 
      field.rowSize !== undefined
    );
  }

  /**
   * Position fields using parallel (matrix-based) system
   * @param fields Array of fields to position
   * @returns PositioningResult with matrix layout
   */
  private positionFieldsParallel(fields: CustomField[]): PositioningResult {
    // Convert fields to positioned fields with default values
    const positionedFields: PositionedField[] = fields.map((field, index) => ({
      ...field,
      gridRow: field.row || 1,
      gridColumn: field.column || 1,
      gridRowSpan: field.rowSize || 1,
      gridColumnSpan: field.colSize || 1,
      originalIndex: index
    }));

    // Calculate matrix dimensions
    const maxRows = Math.max(...positionedFields.map(f => (f.gridRow || 1) + (f.gridRowSpan || 1) - 1));
    const maxColumns = Math.max(...positionedFields.map(f => (f.gridColumn || 1) + (f.gridColumnSpan || 1) - 1));

    // Create matrix
    const matrix: MatrixCell[][] = this.createMatrix(maxRows, maxColumns);

    // Sort fields by priority: response order (originalIndex) for conflicts
    const sortedFields = [...positionedFields].sort((a, b) => (a.originalIndex || 0) - (b.originalIndex || 0));

    // Place fields in matrix
    const placedFields: PositionedField[] = [];
    
    for (const field of sortedFields) {
      const placed = this.placeFieldInMatrix(field, matrix, maxRows, maxColumns);
      if (placed) {
        placedFields.push(placed);
      }
    }

    return {
      useParallelSystem: true,
      fields: placedFields,
      matrix,
      maxRows,
      maxColumns
    };
  }

  /**
   * Position fields using round-robin system (existing logic)
   * @param fields Array of fields to position
   * @param columnCount Number of columns
   * @returns PositioningResult with column-based layout
   */
  private positionFieldsRoundRobin(fields: CustomField[], columnCount: number): PositioningResult {
    const positionedFields: PositionedField[] = fields.map((field, index) => ({
      ...field,
      originalIndex: index
    }));

    const columns: PositionedField[][] = Array.from({ length: columnCount }, () => []);
    
    positionedFields.forEach((field, index) => {
      const colIndex = index % columnCount;
      columns[colIndex].push(field);
    });

    return {
      useParallelSystem: false,
      fields: positionedFields,
      columns
    };
  }

  /**
   * Create an empty matrix with specified dimensions
   * @param rows Number of rows
   * @param columns Number of columns
   * @returns Empty matrix
   */
  private createMatrix(rows: number, columns: number): MatrixCell[][] {
    const matrix: MatrixCell[][] = [];
    
    for (let r = 0; r < rows; r++) {
      matrix[r] = [];
      for (let c = 0; c < columns; c++) {
        matrix[r][c] = {
          field: null,
          occupied: false,
          rowSpan: 1,
          colSpan: 1
        };
      }
    }
    
    return matrix;
  }

  /**
   * Place a field in the matrix at its specified position
   * @param field Field to place
   * @param matrix Matrix to place field in
   * @param maxRows Maximum rows in matrix
   * @param maxColumns Maximum columns in matrix
   * @returns Positioned field if successfully placed, null otherwise
   */
  private placeFieldInMatrix(
    field: PositionedField, 
    matrix: MatrixCell[][], 
    maxRows: number, 
    maxColumns: number
  ): PositionedField | null {
    const row = (field.gridRow || 1) - 1; // Convert to 0-based index
    const col = (field.gridColumn || 1) - 1; // Convert to 0-based index
    const rowSpan = field.gridRowSpan || 1;
    const colSpan = field.gridColumnSpan || 1;

    // Check if position is valid and available
    if (!this.isPositionAvailable(matrix, row, col, rowSpan, colSpan, maxRows, maxColumns)) {
      // If position is not available, try to find next available position
      const nextPosition = this.findNextAvailablePosition(matrix, row, col, rowSpan, colSpan, maxRows, maxColumns);
      if (nextPosition) {
        return this.placeFieldAtPosition(field, matrix, nextPosition.row, nextPosition.col, rowSpan, colSpan);
      }
      return null; // Could not place field
    }

    return this.placeFieldAtPosition(field, matrix, row, col, rowSpan, colSpan);
  }

  /**
   * Check if a position in the matrix is available for a field with given span
   * @param matrix Matrix to check
   * @param row Starting row (0-based)
   * @param col Starting column (0-based)
   * @param rowSpan Number of rows the field spans
   * @param colSpan Number of columns the field spans
   * @param maxRows Maximum rows in matrix
   * @param maxColumns Maximum columns in matrix
   * @returns true if position is available
   */
  private isPositionAvailable(
    matrix: MatrixCell[][], 
    row: number, 
    col: number, 
    rowSpan: number, 
    colSpan: number,
    maxRows: number,
    maxColumns: number
  ): boolean {
    // Check bounds
    if (row < 0 || col < 0 || row + rowSpan > maxRows || col + colSpan > maxColumns) {
      return false;
    }

    // Check if all cells in the span are available
    for (let r = row; r < row + rowSpan; r++) {
      for (let c = col; c < col + colSpan; c++) {
        if (matrix[r][c].occupied) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Find the next available position for a field starting from a given position
   * @param matrix Matrix to search
   * @param startRow Starting row to search from
   * @param startCol Starting column to search from
   * @param rowSpan Number of rows the field spans
   * @param colSpan Number of columns the field spans
   * @param maxRows Maximum rows in matrix
   * @param maxColumns Maximum columns in matrix
   * @returns Next available position or null if none found
   */
  private findNextAvailablePosition(
    matrix: MatrixCell[][], 
    startRow: number, 
    startCol: number, 
    rowSpan: number, 
    colSpan: number,
    maxRows: number,
    maxColumns: number
  ): { row: number, col: number } | null {
    // Search row by row, column by column from the starting position
    for (let r = startRow; r < maxRows; r++) {
      const colStart = r === startRow ? startCol : 0;
      for (let c = colStart; c < maxColumns; c++) {
        if (this.isPositionAvailable(matrix, r, c, rowSpan, colSpan, maxRows, maxColumns)) {
          return { row: r, col: c };
        }
      }
    }

    return null;
  }

  /**
   * Place a field at a specific position in the matrix
   * @param field Field to place
   * @param matrix Matrix to place field in
   * @param row Row position (0-based)
   * @param col Column position (0-based)
   * @param rowSpan Number of rows the field spans
   * @param colSpan Number of columns the field spans
   * @returns Positioned field
   */
  private placeFieldAtPosition(
    field: PositionedField, 
    matrix: MatrixCell[][], 
    row: number, 
    col: number, 
    rowSpan: number, 
    colSpan: number
  ): PositionedField {
    // Mark all cells as occupied
    for (let r = row; r < row + rowSpan; r++) {
      for (let c = col; c < col + colSpan; c++) {
        matrix[r][c] = {
          field: r === row && c === col ? field : null, // Only store field reference in top-left cell
          occupied: true,
          rowSpan: r === row && c === col ? rowSpan : 1,
          colSpan: r === row && c === col ? colSpan : 1
        };
      }
    }

    // Update field with actual placement position
    return {
      ...field,
      gridRow: row + 1, // Convert back to 1-based
      gridColumn: col + 1, // Convert back to 1-based
      gridRowSpan: rowSpan,
      gridColumnSpan: colSpan
    };
  }

  /**
   * Get CSS grid styles for a positioned field
   * @param field Positioned field
   * @returns CSS grid style object
   */
  getFieldGridStyles(field: PositionedField): { [key: string]: string } {
    if (!field.gridRow || !field.gridColumn) {
      return {};
    }

    const styles: { [key: string]: string } = {
      'grid-row': `${field.gridRow}`,
      'grid-column': `${field.gridColumn}`
    };

    if (field.gridRowSpan && field.gridRowSpan > 1) {
      styles['grid-row'] = `${field.gridRow} / span ${field.gridRowSpan}`;
    }

    if (field.gridColumnSpan && field.gridColumnSpan > 1) {
      styles['grid-column'] = `${field.gridColumn} / span ${field.gridColumnSpan}`;
    }

    return styles;
  }
}
